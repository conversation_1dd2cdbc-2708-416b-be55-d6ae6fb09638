# موقع ويب مع نظام تسجيل دخول وقاعدة بيانات

موقع ويب حديث مبني بتقنيات Next.js و React مع نظام مصادقة آمن وقاعدة بيانات PostgreSQL باستخدام Supabase.

## الميزات

- ✅ نظام تسجيل دخول وإنشاء حساب آمن
- ✅ قاعدة بيانات PostgreSQL
- ✅ واجهة مستخدم حديثة مع Tailwind CSS
- ✅ حماية الصفحات (Protected Routes)
- ✅ تصميم متجاوب (Responsive Design)
- ✅ دعم اللغة العربية

## التقنيات المستخدمة

- **Frontend**: Next.js 14, React, TypeScript
- **Styling**: Tailwind CSS
- **Database & Auth**: Supabase
- **Deployment**: Vercel (اختياري)

## التثبيت والإعداد

### 1. تثبيت المتطلبات

```bash
npm install
```

### 2. إعد<PERSON> Supabase

1. اذهب إلى [Supabase](https://supabase.com) وأنشئ حساباً جديداً
2. أنشئ مشروعاً جديداً
3. من لوحة التحكم، اذهب إلى Settings > API
4. انسخ `Project URL` و `anon public key`

### 3. إعداد متغيرات البيئة

قم بتحديث ملف `.env.local`:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 4. تشغيل المشروع

```bash
npm run dev
```

الموقع سيكون متاحاً على: http://localhost:3000

## الصفحات المتاحة

- `/` - الصفحة الرئيسية
- `/login` - صفحة تسجيل الدخول وإنشاء الحساب
- `/dashboard` - لوحة التحكم (محمية)

## كيفية الاستخدام

1. اذهب إلى الصفحة الرئيسية
2. اضغط على "تسجيل الدخول"
3. أنشئ حساباً جديداً أو سجل دخولك
4. ستتم إعادة توجيهك إلى لوحة التحكم

## إضافة ميزات جديدة

يمكنك إضافة المزيد من الميزات مثل:

- إدارة الملف الشخصي
- نظام الأدوار والصلاحيات
- رفع الملفات
- إشعارات في الوقت الفعلي
- API endpoints

## الدعم

إذا واجهت أي مشاكل، تأكد من:

1. تحديث متغيرات البيئة بالقيم الصحيحة من Supabase
2. تفعيل المصادقة في لوحة تحكم Supabase
3. التأكد من تشغيل الخادم على المنفذ الصحيح

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.
