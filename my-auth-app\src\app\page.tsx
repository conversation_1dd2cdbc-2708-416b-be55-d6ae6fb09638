import Link from 'next/link'

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            مرحباً بك في موقعنا
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            موقع ويب حديث مع نظام تسجيل دخول آمن وقاعدة بيانات متقدمة
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Link
              href="/login"
              className="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-3 px-8 rounded-lg transition duration-300 ease-in-out transform hover:scale-105"
            >
              تسجيل الدخول
            </Link>
            <Link
              href="/dashboard"
              className="bg-white hover:bg-gray-50 text-indigo-600 font-bold py-3 px-8 rounded-lg border-2 border-indigo-600 transition duration-300 ease-in-out transform hover:scale-105"
            >
              لوحة التحكم
            </Link>
          </div>
        </div>

        <div className="mt-16 grid md:grid-cols-3 gap-8">
          <div className="bg-white p-6 rounded-lg shadow-lg">
            <div className="text-indigo-600 text-3xl mb-4">🔐</div>
            <h3 className="text-xl font-semibold mb-2">نظام مصادقة آمن</h3>
            <p className="text-gray-600">
              نظام تسجيل دخول متقدم مع حماية عالية المستوى باستخدام Supabase
            </p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-lg">
            <div className="text-indigo-600 text-3xl mb-4">🗄️</div>
            <h3 className="text-xl font-semibold mb-2">قاعدة بيانات قوية</h3>
            <p className="text-gray-600">
              قاعدة بيانات PostgreSQL سريعة وموثوقة لحفظ جميع بياناتك
            </p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-lg">
            <div className="text-indigo-600 text-3xl mb-4">⚡</div>
            <h3 className="text-xl font-semibold mb-2">أداء عالي</h3>
            <p className="text-gray-600">
              مبني بتقنيات حديثة مثل Next.js و React لضمان سرعة الاستجابة
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
