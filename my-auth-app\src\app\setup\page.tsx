'use client'

import { useState } from 'react'
import Link from 'next/link'

export default function Setup() {
  const [supabaseUrl, setSupabaseUrl] = useState('')
  const [supabaseKey, setSupabaseKey] = useState('')
  const [showInstructions, setShowInstructions] = useState(true)

  const handleSave = () => {
    if (supabaseUrl && supabaseKey) {
      alert(`يرجى نسخ هذه القيم إلى ملف .env.local:

NEXT_PUBLIC_SUPABASE_URL=${supabaseUrl}
NEXT_PUBLIC_SUPABASE_ANON_KEY=${supabaseKey}

ثم أعد تشغيل الخادم بالأمر: npm run dev`)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            إعداد Supabase
          </h1>
          <p className="text-gray-600">
            اتبع الخطوات التالية لإعداد قاعدة البيانات والمصادقة
          </p>
        </div>

        {showInstructions && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              📋 خطوات الإعداد
            </h2>
            
            <div className="space-y-6">
              <div className="border-l-4 border-blue-500 pl-4">
                <h3 className="font-semibold text-lg mb-2">1. إنشاء حساب Supabase</h3>
                <p className="text-gray-600 mb-2">
                  اذهب إلى <a href="https://supabase.com" target="_blank" className="text-blue-600 hover:underline">supabase.com</a> وأنشئ حساباً جديداً
                </p>
              </div>

              <div className="border-l-4 border-green-500 pl-4">
                <h3 className="font-semibold text-lg mb-2">2. إنشاء مشروع جديد</h3>
                <ul className="text-gray-600 space-y-1">
                  <li>• اضغط على "New Project"</li>
                  <li>• اختر اسماً للمشروع</li>
                  <li>• اختر كلمة مرور قوية لقاعدة البيانات</li>
                  <li>• اختر المنطقة الأقرب لك</li>
                  <li>• اضغط "Create new project"</li>
                </ul>
              </div>

              <div className="border-l-4 border-yellow-500 pl-4">
                <h3 className="font-semibold text-lg mb-2">3. الحصول على مفاتيح API</h3>
                <ul className="text-gray-600 space-y-1">
                  <li>• من لوحة التحكم، اذهب إلى Settings → API</li>
                  <li>• انسخ "Project URL"</li>
                  <li>• انسخ "anon public" key</li>
                </ul>
              </div>

              <div className="border-l-4 border-purple-500 pl-4">
                <h3 className="font-semibold text-lg mb-2">4. تفعيل المصادقة</h3>
                <ul className="text-gray-600 space-y-1">
                  <li>• اذهب إلى Authentication → Settings</li>
                  <li>• تأكد من تفعيل "Enable email confirmations" (اختياري)</li>
                  <li>• يمكنك إضافة مقدمي خدمة إضافيين مثل Google أو GitHub</li>
                </ul>
              </div>
            </div>

            <button
              onClick={() => setShowInstructions(false)}
              className="mt-6 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              فهمت، أريد إدخال المفاتيح
            </button>
          </div>
        )}

        {!showInstructions && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">إدخال مفاتيح Supabase</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Project URL
                </label>
                <input
                  type="url"
                  value={supabaseUrl}
                  onChange={(e) => setSupabaseUrl(e.target.value)}
                  placeholder="https://your-project.supabase.co"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Anon Public Key
                </label>
                <textarea
                  value={supabaseKey}
                  onChange={(e) => setSupabaseKey(e.target.value)}
                  placeholder="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="flex gap-4">
                <button
                  onClick={handleSave}
                  disabled={!supabaseUrl || !supabaseKey}
                  className="bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  حفظ الإعدادات
                </button>
                
                <button
                  onClick={() => setShowInstructions(true)}
                  className="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700"
                >
                  عرض التعليمات مرة أخرى
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="mt-8 text-center">
          <Link
            href="/"
            className="text-blue-600 hover:text-blue-800 font-medium"
          >
            ← العودة إلى الصفحة الرئيسية
          </Link>
        </div>
      </div>
    </div>
  )
}
