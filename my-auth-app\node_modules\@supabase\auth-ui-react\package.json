{"name": "@supabase/auth-ui-react", "version": "0.4.7", "main": "./dist/index.cjs.js", "module": "./dist/index.es.js", "files": ["dist"], "publishConfig": {"access": "public"}, "types": "./dist/index.d.ts", "type": "module", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.es.js", "require": "./dist/index.cjs.js"}}, "homepage": "https://github.com/supabase-community/auth-ui#readme", "repository": {"type": "git", "url": "git+https://github.com/supabase-community/auth-ui.git"}, "sideEffects": false, "source": "src/index.tsx", "dependencies": {"@stitches/core": "^1.2.8", "prop-types": "^15.7.2", "react": "^18.2.0", "react-dom": "^18.2.0", "@supabase/auth-ui-shared": "0.1.8"}, "peerDependencies": {"@supabase/supabase-js": "^2.21.0"}, "devDependencies": {"@supabase/supabase-js": "^2.21.0", "@types/is-ci": "^3.0.0", "@types/minimist": "^1.2.2", "@types/node": "^18.14.0", "@types/normalize-package-data": "^2.4.1", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@types/semver": "^7.3.13", "@vitejs/plugin-react": "^3.1.0", "typescript": "^4.9.3", "vite": "^4.1.0", "vite-plugin-dts": "2.0.0-beta.1", "tsconfig": "0.0.0"}, "scripts": {"build": "tsc && vite build", "build:tsc": "rm -rf dist && tsc --build"}}