'use client'

import { useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase'
import Link from 'next/link'

export default function Status() {
  const [connectionStatus, setConnectionStatus] = useState<'checking' | 'connected' | 'error'>('checking')
  const [errorMessage, setErrorMessage] = useState('')
  const [supabaseConfig, setSupabaseConfig] = useState({
    url: '',
    hasKey: false
  })

  useEffect(() => {
    checkConnection()
  }, [])

  const checkConnection = async () => {
    try {
      // التحقق من إعدادات Supabase
      const url = process.env.NEXT_PUBLIC_SUPABASE_URL || ''
      const key = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''
      
      setSupabaseConfig({
        url: url,
        hasKey: key.length > 10
      })

      if (url === 'https://placeholder.supabase.co' || key === 'placeholder-key') {
        setConnectionStatus('error')
        setErrorMessage('لم يتم تكوين Supabase بعد. يرجى إعداد المتغيرات في ملف .env.local')
        return
      }

      // محاولة الاتصال بـ Supabase
      const { data, error } = await supabase.from('_test_').select('*').limit(1)
      
      if (error && error.message.includes('relation "_test_" does not exist')) {
        // هذا طبيعي - يعني أن الاتصال يعمل لكن الجدول غير موجود
        setConnectionStatus('connected')
      } else if (error) {
        setConnectionStatus('error')
        setErrorMessage(error.message)
      } else {
        setConnectionStatus('connected')
      }
    } catch (error: any) {
      setConnectionStatus('error')
      setErrorMessage(error.message || 'خطأ غير معروف')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            حالة الاتصال
          </h1>
          <p className="text-gray-600">
            التحقق من حالة الاتصال مع Supabase
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="space-y-6">
            {/* حالة الاتصال */}
            <div className="flex items-center justify-between p-4 rounded-lg border">
              <div>
                <h3 className="font-semibold">حالة الاتصال</h3>
                <p className="text-sm text-gray-600">الاتصال مع قاعدة البيانات</p>
              </div>
              <div className="flex items-center">
                {connectionStatus === 'checking' && (
                  <div className="flex items-center text-yellow-600">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600 mr-2"></div>
                    جاري التحقق...
                  </div>
                )}
                {connectionStatus === 'connected' && (
                  <div className="flex items-center text-green-600">
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    متصل
                  </div>
                )}
                {connectionStatus === 'error' && (
                  <div className="flex items-center text-red-600">
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                    خطأ في الاتصال
                  </div>
                )}
              </div>
            </div>

            {/* تفاصيل الإعدادات */}
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                <span className="font-medium">Supabase URL:</span>
                <span className="text-sm text-gray-600 font-mono">
                  {supabaseConfig.url || 'غير محدد'}
                </span>
              </div>
              
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                <span className="font-medium">API Key:</span>
                <span className="text-sm text-gray-600">
                  {supabaseConfig.hasKey ? '✅ محدد' : '❌ غير محدد'}
                </span>
              </div>
            </div>

            {/* رسالة الخطأ */}
            {connectionStatus === 'error' && errorMessage && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <h4 className="font-semibold text-red-800 mb-2">تفاصيل الخطأ:</h4>
                <p className="text-red-700 text-sm">{errorMessage}</p>
              </div>
            )}

            {/* الإجراءات */}
            <div className="flex gap-4 pt-4">
              <button
                onClick={checkConnection}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
              >
                إعادة التحقق
              </button>
              
              {connectionStatus === 'error' && (
                <Link
                  href="/setup"
                  className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
                >
                  إعداد Supabase
                </Link>
              )}
            </div>
          </div>
        </div>

        <div className="mt-8 text-center">
          <Link
            href="/"
            className="text-blue-600 hover:text-blue-800 font-medium"
          >
            ← العودة إلى الصفحة الرئيسية
          </Link>
        </div>
      </div>
    </div>
  )
}
