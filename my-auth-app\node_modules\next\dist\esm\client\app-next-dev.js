// TODO-APP: hydration warning
import './app-webpack';
import { appBootstrap } from './app-bootstrap';
import { initializeDevBuildIndicatorForAppRouter } from './dev/dev-build-indicator/initialize-for-app-router';
const instrumentationHooks = require('../lib/require-instrumentation-client');
appBootstrap(()=>{
    const { hydrate } = require('./app-index');
    hydrate(instrumentationHooks);
    initializeDevBuildIndicatorForAppRouter();
});

//# sourceMappingURL=app-next-dev.js.map