import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key'

// التحقق من وجود القيم الصحيحة
if (supabaseUrl === 'https://placeholder.supabase.co' || supabaseAnonKey === 'placeholder-key') {
  console.warn('⚠️ يرجى تحديث متغيرات البيئة في ملف .env.local بقيم Supabase الصحيحة')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)
